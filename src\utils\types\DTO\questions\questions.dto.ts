import { UserRole } from "../../enums/user-role";
import { MetaDTO } from "../meta.dto";

export type GetAllQuestionsDto = {
  meta: MetaDTO;
  data: GetQuestionDto[];
};

export type GetQuestionDto = {
  secureId: string;
  order: number;
  videoId: string;
  essayQuestion: GetEssayQuestionDto;
  objectiveQuestion: GetObjectiveQuestionDto;
  updatedAt: string;
  createdAt: string;
};

type GetEssayQuestionDto = {
  secureId: string;
  statement: string;
};

type GetObjectiveQuestionDto = {
  secureId: string;
  statement: string;
  variables: Variable[];
};

type Variable = {
  secureId: string;
  name: string;
  value: string;
};
