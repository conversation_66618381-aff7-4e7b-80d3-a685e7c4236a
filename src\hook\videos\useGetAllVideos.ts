import { api } from "@/services/api";
import { GetAllVideosDto } from "@/utils/types/DTO/videos/videos.dto";
import { useQuery } from "@tanstack/react-query";


async function getAllVideos(searchTerm?: string) {
  const { data } = await api.get<GetAllVideosDto>(`/management/video`, {
    params: {
      search: searchTerm,
    },
  });
  return data;
}

export function useGetAllVideos(searchTerm?: string) {
  return useQuery({
    queryKey: ["videos", searchTerm],
    queryFn: async () => await getAllVideos(searchTerm),
  });
}