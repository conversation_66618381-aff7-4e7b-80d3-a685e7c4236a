"use client";
import Default<PERSON><PERSON>on from "@/components/global/buttons/button";
import {
  Box,
  Field,
  FileUpload,
  Flex,
  HStack,
  Icon,
  Input,
  Stack,
  Table,
  Text,
  VStack,
} from "@chakra-ui/react";
import { useState } from "react";
import {
  LuSearch,
  LuPlus,
  LuTrash2,
  LuPencil,
  LuVideoOff,
  LuVideo,
  LuUpload,
} from "react-icons/lu";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { formatInTimeZone } from "date-fns-tz";
import BasicModal from "@/components/global/modal/basic-modal";
import { useMutation } from "@tanstack/react-query";
import { toaster } from "@/components/ui/toaster";
import { api } from "@/services/api";
import { queryClient } from "@/services/queryClient";
import { useGetAllVideos } from "@/hook/videos/useGetAllVideos";
import { GetVideoDto } from "@/utils/types/DTO/videos/videos.dto";
import FormCombobox from "@/components/global/combobox/form-combobox";

const SUPPORTED_VIDEO_TYPES = [
  "video/mp4",
  "video/avi",
  "video/mov",
  "video/wmv",
  "video/flv",
  "video/webm",
  "video/mkv",
];

const MAX_FILE_SIZE = 100 * 1024 * 1024;

type NewVideoFormData = {
  secureId?: string;
  title: string;
  type: "UPLOAD" | "URL";
  file?: FileList;
  url?: string;
};

const NewVideoSchema: yup.ObjectSchema<NewVideoFormData> = yup.object().shape({
  secureId: yup.string().optional(),
  title: yup
    .string()
    .min(3, "O título deve ter no mínimo 3 caracteres")
    .max(50, "O título deve ter no máximo 50 caracteres")
    .required("O título do vídeo é obrigatório"),
  type: yup
    .string()
    .oneOf(["UPLOAD", "URL"], "Tipo de vídeo deve ser selecionado")
    .required("O tipo do vídeo é obrigatório"),
  file: yup.mixed<FileList>().when("type", {
    is: "UPLOAD",
    then: (schema) =>
      schema
        .test("fileRequired", "O vídeo é obrigatório", function (value) {
          if (this.parent.secureId) {
            return true;
          }
          return value instanceof FileList && value.length > 0;
        })
        .test(
          "fileType",
          "Formato de arquivo não suportado. Use: MP4, AVI, MOV, WMV, FLV, WebM, MKV",
          (value) => {
            if (!value || value.length === 0) return true;
            const file = value[0];
            return SUPPORTED_VIDEO_TYPES.includes(file.type);
          }
        )
        .test("fileSize", "O arquivo deve ter no máximo 100MB", (value) => {
          if (!value || value.length === 0) return true;
          const file = value[0];
          return file.size <= MAX_FILE_SIZE;
        }),
    otherwise: (schema) => schema.notRequired(),
  }),
  url: yup.string().when("type", {
    is: "URL",
    then: (schema) =>
      schema.url("URL inválida").required("A url é obrigatória"),
    otherwise: (schema) => schema.notRequired(),
  }),
});

export default function Videos() {
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedVideo, setSelectedVideo] = useState<GetVideoDto | null>(null);
  const [searchTerm, setSearchTerm] = useState("");

  const { data: videosData } = useGetAllVideos(searchTerm);

  const {
    register,
    handleSubmit,
    reset,
    setValue,
    watch,
    formState,
    formState: { errors, isSubmitting },
  } = useForm<NewVideoFormData>({
    resolver: yupResolver(NewVideoSchema),
    defaultValues: {
      secureId: undefined,
      title: "",
      type: undefined,
      file: undefined,
      url: "",
    },
  });

  const addVideo = useMutation({
    mutationFn: async (data: NewVideoFormData) => {
      const formData = new FormData();
      formData.append("title", data.title);

      if (data.type === "UPLOAD" && data.file && data.file.length > 0) {
        formData.append("file", data.file[0]);
      } else if (data.type === "URL" && data.url) {
        formData.append("url", data.url);
      }

      await api.post("/management/video", formData, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      });
    },
    onSuccess: () => {
      toaster.success({
        title: "Vídeo adicionado com sucesso!",
      });
    },
  });

  const editVideo = useMutation({
    mutationFn: async (data: NewVideoFormData) => {
      const formData = new FormData();
      formData.append("title", data.title);

      if (data.file && data.file.length > 0) {
        formData.append("file", data.file[0]);
      } else if (data.url && data.url !== selectedVideo?.url) {
        formData.append("url", data.url);
      }
      await api.patch(
        `/management/video/${selectedVideo?.secureId}`,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        }
      );
    },
    onSuccess: () => {
      toaster.success({
        title: "Vídeo editado com sucesso!",
      });
    },
  });

  const deleteVideo = useMutation({
    mutationFn: async () => {
      await api.delete(`/management/video/${selectedVideo?.secureId}`);
    },
    onSuccess: () => {
      toaster.success({
        title: "Vídeo excluído com sucesso!",
      });
    },
  });

  const handleAddVideo = async (data: NewVideoFormData) => {
    try {
      await addVideo.mutateAsync(data);
      queryClient.invalidateQueries({ queryKey: ["videos"] });
      setIsAddModalOpen(false);
      reset();
    } catch (e) {}
  };

  const handleEditVideo = async (data: NewVideoFormData) => {
    try {
      await editVideo.mutateAsync(data);
      queryClient.invalidateQueries({ queryKey: ["videos"] });
      setIsEditModalOpen(false);
      reset();
    } catch (e) {}
  };

  const handleDeleteVideo = async () => {
    try {
      await deleteVideo.mutateAsync();
      queryClient.invalidateQueries({ queryKey: ["videos"] });
      setIsDeleteModalOpen(false);
    } catch (e) {}
  };

  const toggleVideoStatus = useMutation({
    mutationFn: async (video: GetVideoDto) => {
      const newStatus = !video.isActive;
      await api.patch(`/management/video/${video.secureId}`, {
        isActive: String(newStatus),
      });
      return newStatus;
    },
    onSuccess: (newStatus) => {
      queryClient.invalidateQueries({ queryKey: ["videos"] });
      toaster.success({
        title: `Vídeo ${newStatus ? "ativado" : "inativado"} com sucesso!`,
      });
      setSelectedVideo(null);
    },
    onError: (error) => {
      console.error("Erro ao alterar o status do vídeo", error);
      toaster.error({
        title: "Erro ao alterar o status",
        description:
          "Não foi possível alterar o status do vídeo. Tente novamente.",
      });
      setSelectedVideo(null);
    },
  });

  const handleToggleVideoStatus = (video: GetVideoDto) => {
    setSelectedVideo(video);
    toggleVideoStatus.mutate(video);
  };

  const handleOpenAddModal = () => {
    reset();
    setIsAddModalOpen(true);
  };

  const handleOpenEditModal = (video: GetVideoDto) => {
    setSelectedVideo(video);
    setValue("secureId", video.secureId);
    setValue("title", video.title);
    setValue("type", video.type);
    if (video.type === "URL") {
      setValue("url", video.url);
    } else {
      setValue("file", undefined);
    }
    setIsEditModalOpen(true);
  };

  const handleOpenDeleteModal = (video: GetVideoDto) => {
    setSelectedVideo(video);
    setIsDeleteModalOpen(true);
  };

  return (
    <Flex flex={1} position={"relative"} overflow={"hidden"} p={4}>
      <Stack w="100%" gap={6}>
        {/* Header */}
        <Text fontSize="2xl" fontWeight="bold" color="white">
          Vídeos
        </Text>

        {/* Search and Filters */}
        <HStack gap={4} justify="space-between">
          <Box position="relative" flex={1} maxW="400px">
            <Input
              placeholder="Buscar..."
              bg="gray.800"
              border="1px solid"
              borderColor="gray.600"
              color="white"
              _placeholder={{ color: "gray.400" }}
              _focus={{
                borderColor: "blue.400",
                boxShadow: "0 0 0 1px #3182ce",
              }}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
            <Box
              position="absolute"
              right={3}
              top="50%"
              transform="translateY(-50%)"
              color="gray.400"
            >
              <LuSearch />
            </Box>
          </Box>
          <DefaultButton onClick={handleOpenAddModal} size="md">
            <LuPlus />
            Adicionar Vídeo
          </DefaultButton>
        </HStack>

        {/* Table */}
        <Box
          bg="gray.800"
          borderRadius="lg"
          border="1px solid"
          borderColor="gray.600"
          overflow="hidden"
        >
          <Table.Root size="md" variant="outline">
            <Table.Header bg="gray.700">
              <Table.Row>
                <Table.ColumnHeader color="white" fontWeight="bold">
                  Título
                </Table.ColumnHeader>
                <Table.ColumnHeader color="white" fontWeight="bold">
                  Tipo
                </Table.ColumnHeader>
                <Table.ColumnHeader color="white" fontWeight="bold">
                  Url
                </Table.ColumnHeader>
                <Table.ColumnHeader color="white" fontWeight="bold">
                  Data de Criação
                </Table.ColumnHeader>
                <Table.ColumnHeader color="white" fontWeight="bold">
                  <Flex justify="center">Ações</Flex>
                </Table.ColumnHeader>
              </Table.Row>
            </Table.Header>
            <Table.Body>
              {videosData?.data?.map((item) => (
                <Table.Row key={item.secureId} _hover={{ bg: "gray.700" }}>
                  <Table.Cell>
                    <Text color="gray.300">{item.title}</Text>
                  </Table.Cell>
                  <Table.Cell>
                    <Text color="gray.300">{item.type}</Text>
                  </Table.Cell>
                  <Table.Cell>
                    <Text color="gray.300">{item.url}</Text>
                  </Table.Cell>
                  <Table.Cell>
                    <Text color="gray.300" maxW="200px">
                      {formatInTimeZone(
                        item.createdAt,
                        "America/Sao_Paulo",
                        "dd/MM/yyyy HH:mm:ss"
                      )}
                    </Text>
                  </Table.Cell>
                  <Table.Cell>
                    <HStack gap={2} justifyContent={"center"}>
                      <DefaultButton
                        tooltipContent="Editar"
                        buttonColor="#156082"
                        size="sm"
                        onClick={() => handleOpenEditModal(item)}
                      >
                        <LuPencil />
                      </DefaultButton>
                      <DefaultButton
                        tooltipContent="Excluir"
                        buttonColor="red.500"
                        size="sm"
                        onClick={() => handleOpenDeleteModal(item)}
                      >
                        <LuTrash2 />
                      </DefaultButton>
                      <DefaultButton
                        tooltipContent={item.isActive ? "Inativar" : "Ativar"}
                        buttonColor={item.isActive ? "gray.500" : "green.600"}
                        size="sm"
                        onClick={() => handleToggleVideoStatus(item)}
                        loading={
                          toggleVideoStatus.isPending &&
                          selectedVideo?.secureId === item.secureId
                        }
                      >
                        {item.isActive ? <LuVideoOff /> : <LuVideo />}
                      </DefaultButton>
                    </HStack>
                  </Table.Cell>
                </Table.Row>
              )) || (
                <Table.Row>
                  <Table.Cell colSpan={4}>
                    <Text color="gray.400" textAlign="center" py={4}>
                      Nenhuma categoria encontrada
                    </Text>
                  </Table.Cell>
                </Table.Row>
              )}
            </Table.Body>
          </Table.Root>
        </Box>
      </Stack>

      {/* Add Video Modal */}
      <BasicModal
        open={isAddModalOpen}
        setOpen={setIsAddModalOpen}
        title="Adicionar Novo Vídeo"
        size="md"
        asForm={true}
        handleSubmit={handleSubmit(handleAddVideo)}
        isSubmitting={formState.isSubmitting}
        confirmText="Adicionar"
        cancelText="Cancelar"
        placement="center"
      >
        <VStack gap={4} align="stretch">
          <Field.Root invalid={!!formState.errors.title}>
            <Field.Label color="white">Título do Vídeo</Field.Label>
            <Input
              placeholder="Digite o título do vídeo"
              bg="gray.700"
              border="1px solid"
              borderColor="gray.600"
              color="white"
              _placeholder={{ color: "gray.400" }}
              _focus={{
                borderColor: "blue.400",
                boxShadow: "0 0 0 1px #3182ce",
              }}
              {...register("title")}
            />
            <Field.ErrorText>{formState.errors.title?.message}</Field.ErrorText>
          </Field.Root>

          <FormCombobox
            label="Como deseja adicionar o vídeo?"
            placeholder="Selecione uma opção"
            options={[
              { label: "Upload", value: "UPLOAD" },
              { label: "Url", value: "URL" },
            ]}
            value={watch("type")}
            onValueChange={(value) => setValue("type", value as any)}
            error={errors.type}
            isInvalid={!!errors.type}
          />

          {watch("type") === "UPLOAD" && (
            <Field.Root invalid={!!formState.errors.file}>
              <Field.Label color="white">Upload do Vídeo</Field.Label>
              <FileUpload.Root
                maxW="lg"
                alignItems="stretch"
                maxFiles={1}
                accept={SUPPORTED_VIDEO_TYPES.join(",")}
              >
                <FileUpload.HiddenInput {...register("file")} />
                <FileUpload.Dropzone bgColor={"gray.700"}>
                  <Icon size="md" color="fg.muted">
                    <LuUpload />
                  </Icon>
                  <FileUpload.DropzoneContent>
                    <Box>
                      Arraste e solte o vídeo aqui ou clique para selecionar
                    </Box>
                    <Text fontSize="sm" color="gray.400" mt={2}>
                      Formatos suportados: MP4, AVI, MOV, WMV, FLV, WebM, MKV
                    </Text>
                  </FileUpload.DropzoneContent>
                </FileUpload.Dropzone>
                <FileUpload.List />
              </FileUpload.Root>
              <Field.ErrorText>
                {formState.errors.file?.message}
              </Field.ErrorText>
            </Field.Root>
          )}

          {watch("type") === "URL" && (
            <Field.Root invalid={!!formState.errors.url}>
              <Field.Label color="white">Url do Vídeo</Field.Label>
              <Input
                placeholder="Cole a url do vídeo"
                bg="gray.700"
                border="1px solid"
                borderColor="gray.600"
                color="white"
                _placeholder={{ color: "gray.400" }}
                _focus={{
                  borderColor: "blue.400",
                  boxShadow: "0 0 0 1px #3182ce",
                }}
                {...register("url")}
              />
              <Field.ErrorText>{formState.errors.url?.message}</Field.ErrorText>
            </Field.Root>
          )}
        </VStack>
      </BasicModal>

      {/* Edit Video Modal */}
      <BasicModal
        open={isEditModalOpen}
        setOpen={setIsEditModalOpen}
        title="Editar Vídeo"
        size="md"
        asForm={true}
        handleSubmit={handleSubmit(handleEditVideo)}
        isSubmitting={formState.isSubmitting}
        confirmText="Salvar"
        cancelText="Cancelar"
        placement="center"
      >
        <VStack gap={4} align="stretch">
          <Field.Root invalid={!!formState.errors.title}>
            <Field.Label color="white">Título do Vídeo</Field.Label>
            <Input
              placeholder="Digite o título do vídeo"
              bg="gray.700"
              border="1px solid"
              borderColor="gray.600"
              color="white"
              _placeholder={{ color: "gray.400" }}
              _focus={{
                borderColor: "blue.400",
                boxShadow: "0 0 0 1px #3182ce",
              }}
              {...register("title")}
            />
            <Field.ErrorText>{formState.errors.title?.message}</Field.ErrorText>
          </Field.Root>

          {selectedVideo?.type === "UPLOAD" && (
            <Field.Root invalid={!!formState.errors.file}>
              <Field.Label color="white">
                Substituir Vídeo (Opcional)
              </Field.Label>
              <FileUpload.Root
                maxW="lg"
                alignItems="stretch"
                maxFiles={1}
                accept={SUPPORTED_VIDEO_TYPES.join(",")}
              >
                <FileUpload.HiddenInput {...register("file")} />
                <FileUpload.Dropzone bgColor={"gray.700"}>
                  <Icon size="md" color="fg.muted">
                    <LuUpload />
                  </Icon>
                  <FileUpload.DropzoneContent>
                    <Box>
                      Arraste e solte um novo vídeo aqui ou clique para
                      selecionar
                    </Box>
                    <Text fontSize="sm" color="gray.400" mt={2}>
                      Formatos suportados: MP4, AVI, MOV, WMV, FLV, WebM, MKV
                    </Text>
                  </FileUpload.DropzoneContent>
                </FileUpload.Dropzone>
                <FileUpload.List />
              </FileUpload.Root>
              <Field.ErrorText>
                {formState.errors.file?.message}
              </Field.ErrorText>
            </Field.Root>
          )}

          {selectedVideo?.type === "URL" && (
            <Field.Root invalid={!!formState.errors.url}>
              <Field.Label color="white">Substituir URL (Opcional)</Field.Label>
              <Input
                placeholder="Cole a nova url do vídeo"
                bg="gray.700"
                border="1px solid"
                borderColor="gray.600"
                color="white"
                _placeholder={{ color: "gray.400" }}
                _focus={{
                  borderColor: "blue.400",
                  boxShadow: "0 0 0 1px #3182ce",
                }}
                {...register("url")}
              />
              <Field.ErrorText>{formState.errors.url?.message}</Field.ErrorText>
            </Field.Root>
          )}
        </VStack>
      </BasicModal>

      {/* Delete Video Modal */}
      <BasicModal
        open={isDeleteModalOpen}
        setOpen={setIsDeleteModalOpen}
        title="Excluir Vídeo"
        size="sm"
        handleConfirm={handleDeleteVideo}
        confirmText="Excluir"
        cancelText="Cancelar"
        placement="center"
        confirmButtonColor="red.500"
      >
        <VStack gap={4} align="center">
          <Text fontSize="md" color="white" textAlign="center">
            Você tem certeza que deseja excluir o vídeo{" "}
            <Text as="span" fontWeight="bold" color="red.400">
              "{selectedVideo?.title}"
            </Text>
            ?
          </Text>
          <Text fontSize="sm" color="gray.400" textAlign="center">
            Esta ação não pode ser desfeita.
          </Text>
        </VStack>
      </BasicModal>
    </Flex>
  );
}
