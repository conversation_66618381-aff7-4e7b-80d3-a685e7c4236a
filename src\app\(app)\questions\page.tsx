"use client";
import Defa<PERSON><PERSON><PERSON>on from "@/components/global/buttons/button";
import {
  Box,
  Field,
  Flex,
  HStack,
  Icon,
  Input,
  InputGroup,
  Stack,
  Table,
  Tabs,
  Text,
  Textarea,
  VStack,
} from "@chakra-ui/react";
import { useState } from "react";
import { LuSearch, LuPlus, LuTrash2, LuPencil } from "react-icons/lu";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { formatInTimeZone } from "date-fns-tz";
import BasicModal from "@/components/global/modal/basic-modal";
import { useMutation } from "@tanstack/react-query";
import FormCombobox from "@/components/global/combobox/form-combobox";
import { useGetAllQuestions } from "@/hook/questions/useGetAllQuestions";
import { GetQuestionDto } from "@/utils/types/DTO/questions/questions.dto";
import { useGetAllVideos } from "@/hook/videos/useGetAllVideos";
import { useGetAllVariables } from "@/hook/variables/useGetAllVariables";
import { toaster } from "@/components/ui/toaster";
import { api } from "@/services/api";
import { queryClient } from "@/services/queryClient";

type VariableItem = {
  variable: string;
  percent: number;
};

type NewQuestionFormData = {
  questionType: "essay" | "objective";
  essayStatement?: string;
  objectiveStatement?: string;
  variables?: VariableItem[];
  videoId: string;
  order: number;
  // Temporary fields for adding variables
  selectedVariable?: string;
  variablePercent?: number;
};

const VariableItemSchema = yup.object().shape({
  variable: yup.string().required("A variável é obrigatória."),
  percent: yup
    .number()
    .typeError("A porcentagem deve ser um número.")
    .min(0, "A porcentagem não pode ser negativa.")
    .max(100, "A porcentagem não pode ser maior que 100.")
    .required("A porcentagem é obrigatória."),
});

const NewQuestionSchema = yup.object().shape({
  questionType: yup
    .string()
    .oneOf(["essay", "objective"], "Tipo de questão inválido.")
    .required("O tipo de questão é obrigatório."),
  essayStatement: yup.string().when("questionType", {
    is: "essay",
    then: (schema) =>
      schema.required("O enunciado da questão dissertativa é obrigatório."),
    otherwise: (schema) => schema.notRequired(),
  }),
  objectiveStatement: yup.string().when("questionType", {
    is: "objective",
    then: (schema) =>
      schema.required("O enunciado da questão objetiva é obrigatório."),
    otherwise: (schema) => schema.notRequired(),
  }),
  variables: yup
    .array()
    .of(VariableItemSchema)
    .when("questionType", {
      is: "objective",
      then: (schema) =>
        schema
          .min(1, "Adicione pelo menos uma variável.")
          .test(
            "sum-to-100",
            "A soma das porcentagens deve ser 100%",
            function (variables) {
              if (!variables || variables.length === 0) return true;
              const sum = variables.reduce(
                (acc, item) => acc + (item?.percent || 0),
                0
              );
              return sum === 100;
            }
          ),
      otherwise: (schema) => schema.notRequired(),
    }),
  videoId: yup
    .string()
    .uuid("ID do vídeo inválido.")
    .required("O ID do vídeo é obrigatório."),
  order: yup
    .number()
    .typeError("A ordem deve ser um número.")
    .positive("A ordem deve ser um número positivo.")
    .integer("A ordem deve ser um número inteiro.")
    .required("A ordem é obrigatória."),
  selectedVariable: yup.string().notRequired(),
  variablePercent: yup.number().notRequired(),
});

export default function Questions() {
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedQuestion, setSelectedQuestion] =
    useState<GetQuestionDto | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [currentQuestionType, setCurrentQuestionType] = useState<
    "essay" | "objective"
  >("essay");

  const { data: questionsData } = useGetAllQuestions(searchTerm);
  const { data: videosData } = useGetAllVideos();
  const { data: variablesResponse } = useGetAllVariables();

  const variablesData = variablesResponse?.data || [];

  const {
    register,
    handleSubmit,
    reset,
    setValue,
    watch,
    getValues,
    formState,
    formState: { errors, isSubmitting },
  } = useForm({
    resolver: yupResolver(NewQuestionSchema),
    defaultValues: {
      questionType: "essay",
      essayStatement: "",
      objectiveStatement: "",
      variables: [],
      videoId: "",
      order: 1,
      selectedVariable: "",
      variablePercent: 0,
    },
  });

  const handleAddVariable = () => {
    const selectedVariable = watch("selectedVariable");
    const variablePercent = watch("variablePercent");

    if (!selectedVariable || !variablePercent) {
      toaster.error({
        title: "Erro",
        description: "Selecione uma variável e informe a porcentagem.",
      });
      return;
    }

    const currentVariables = watch("variables") || [];
    const newVariable: VariableItem = {
      variable: selectedVariable,
      percent: variablePercent,
    };

    setValue("variables", [...currentVariables, newVariable]);
    setValue("selectedVariable", "");
    setValue("variablePercent", 0);
  };

  const handleRemoveVariable = (index: number) => {
    const currentVariables = watch("variables") || [];
    const updatedVariables = currentVariables.filter((_, i) => i !== index);
    setValue("variables", updatedVariables);
  };

  const addQuestion = useMutation({
    mutationFn: async (data: any) => {
      const payload: any = {};

      if (data.questionType === "essay") {
        payload.essayQuestion = {
          statement: data.essayStatement,
        };
      } else {
        payload.objectiveQuestion = {
          statement: data.objectiveStatement,
          variables: data.variables,
        };
      }

      payload.videoId = data.videoId;
      payload.order = data.order;

      await api.post("/management/question", payload);
    },
    onSuccess: () => {
      toaster.success({
        title: "Questão adicionada com sucesso!",
      });
    },
  });

  const editQuestion = useMutation({
    mutationFn: async (data: any) => {
      const payload: any = {};

      if (data.questionType === "essay") {
        payload.essayQuestion = {
          statement: data.essayStatement,
        };
      } else {
        payload.objectiveQuestion = {
          statement: data.objectiveStatement,
          variables: data.variables,
        };
      }

      payload.videoId = data.videoId;
      payload.order = data.order;

      await api.patch(
        `/management/question/${selectedQuestion?.secureId}`,
        payload
      );
    },
    onSuccess: () => {
      toaster.success({
        title: "Questão editada com sucesso!",
      });
    },
  });

  const deleteQuestion = useMutation({
    mutationFn: async () => {
      await api.delete(`/management/question/${selectedQuestion?.secureId}`);
    },
    onSuccess: () => {
      toaster.success({
        title: "Questão excluída com sucesso!",
      });
    },
  });

  const handleAddQuestion = async (data: any) => {
    try {
      await addQuestion.mutateAsync(data);
      queryClient.invalidateQueries({ queryKey: ["questions"] });
      setIsAddModalOpen(false);
      reset();
    } catch (e) {
      console.error("Erro ao adicionar questão", e);
    }
  };

  const handleEditQuestion = async (data: any) => {
    try {
      await editQuestion.mutateAsync(data);
      queryClient.invalidateQueries({ queryKey: ["questions"] });
      setIsEditModalOpen(false);
      reset();
    } catch (e) {
      console.error("Erro ao editar questão", e);
    }
  };

  const handleDeleteQuestion = async () => {
    try {
      await deleteQuestion.mutateAsync();
      queryClient.invalidateQueries({ queryKey: ["questions"] });
      setIsDeleteModalOpen(false);
    } catch (e) {
      console.error("Erro ao excluir questão", e);
    }
  };

  const handleOpenAddModal = () => {
    reset();
    setIsAddModalOpen(true);
  };

  const handleOpenEditModal = (question: GetQuestionDto) => {
    setSelectedQuestion(question);

    // Determine question type and populate form
    if (question.essayQuestion?.statement) {
      setValue("questionType", "essay");
      setValue("essayStatement", question.essayQuestion.statement);
      setCurrentQuestionType("essay");
    } else if (question.objectiveQuestion?.statement) {
      setValue("questionType", "objective");
      setValue("objectiveStatement", question.objectiveQuestion.statement);
      setValue(
        "variables",
        question.objectiveQuestion.variables?.map((v) => ({
          variable: v.secureId,
          percent: parseInt(v.value) || 0,
        })) || []
      );
      setCurrentQuestionType("objective");
    }

    setValue("videoId", question.videoId);
    setValue("order", question.order);

    setIsEditModalOpen(true);
  };

  const handleOpenDeleteModal = (question: GetQuestionDto) => {
    setSelectedQuestion(question);
    setIsDeleteModalOpen(true);
  };

  return (
    <Flex flex={1} position={"relative"} overflow={"hidden"} p={4}>
      <Stack w="100%" gap={6}>
        {/* Header */}
        <Text fontSize="2xl" fontWeight="bold" color="white">
          Questões
        </Text>

        {/* Search and Filters */}
        <HStack gap={4} justify="space-between">
          <Box position="relative" flex={1} maxW="400px">
            <Input
              placeholder="Buscar..."
              bg="gray.800"
              border="1px solid"
              borderColor="gray.600"
              color="white"
              _placeholder={{ color: "gray.400" }}
              _focus={{
                borderColor: "blue.400",
                boxShadow: "0 0 0 1px #3182ce",
              }}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
            <Box
              position="absolute"
              right={3}
              top="50%"
              transform="translateY(-50%)"
              color="gray.400"
            >
              <LuSearch />
            </Box>
          </Box>
          <DefaultButton onClick={handleOpenAddModal} size="md">
            <LuPlus />
            Adicionar Questões
          </DefaultButton>
        </HStack>

        {/* Table */}
        <Box
          bg="gray.800"
          borderRadius="lg"
          border="1px solid"
          borderColor="gray.600"
          overflow="hidden"
        >
          <Table.Root size="md" variant="outline">
            <Table.Header bg="gray.700">
              <Table.Row>
                <Table.ColumnHeader color="white" fontWeight="bold">
                  Enunciado Dissertativa
                </Table.ColumnHeader>
                <Table.ColumnHeader color="white" fontWeight="bold">
                  Enunciado Objetiva
                </Table.ColumnHeader>
                <Table.ColumnHeader color="white" fontWeight="bold">
                  Ordem
                </Table.ColumnHeader>
                <Table.ColumnHeader color="white" fontWeight="bold">
                  Data de Criação
                </Table.ColumnHeader>
                <Table.ColumnHeader color="white" fontWeight="bold">
                  <Flex justify="center">Ações</Flex>
                </Table.ColumnHeader>
              </Table.Row>
            </Table.Header>
            <Table.Body>
              {questionsData?.data?.map((item) => (
                <Table.Row key={item.secureId} _hover={{ bg: "gray.700" }}>
                  <Table.Cell>
                    <Text color="gray.300">{item.essayQuestion.statement}</Text>
                  </Table.Cell>
                  <Table.Cell>
                    <Text color="gray.300">
                      {item.objectiveQuestion.statement}
                    </Text>
                  </Table.Cell>
                  <Table.Cell>
                    <Text color="gray.300">{item.order}</Text>
                  </Table.Cell>
                  <Table.Cell>
                    <Text color="gray.300" maxW="200px">
                      {formatInTimeZone(
                        item.createdAt,
                        "America/Sao_Paulo",
                        "dd/MM/yyyy HH:mm:ss"
                      )}
                    </Text>
                  </Table.Cell>
                  <Table.Cell>
                    <HStack gap={2} justifyContent={"center"}>
                      <DefaultButton
                        tooltipContent="Editar"
                        buttonColor="#156082"
                        size="sm"
                        onClick={() => handleOpenEditModal(item)}
                      >
                        <LuPencil />
                      </DefaultButton>
                      <DefaultButton
                        tooltipContent="Excluir"
                        buttonColor="red.500"
                        size="sm"
                        onClick={() => handleOpenDeleteModal(item)}
                      >
                        <LuTrash2 />
                      </DefaultButton>
                    </HStack>
                  </Table.Cell>
                </Table.Row>
              )) || (
                <Table.Row>
                  <Table.Cell colSpan={4}>
                    <Text color="gray.400" textAlign="center" py={4}>
                      Nenhuma questão encontrada
                    </Text>
                  </Table.Cell>
                </Table.Row>
              )}
            </Table.Body>
          </Table.Root>
        </Box>
      </Stack>

      {/* Add Question Modal */}
      <BasicModal
        open={isAddModalOpen}
        setOpen={setIsAddModalOpen}
        title="Adicionar Grupo de Questões"
        size="lg"
        asForm={true}
        handleSubmit={handleSubmit(handleAddQuestion)}
        isSubmitting={formState.isSubmitting}
        confirmText="Adicionar"
        cancelText="Cancelar"
        placement="center"
      >
        <HStack gap={4} align="stretch">
          <FormCombobox
            label="Vídeo de Introdução"
            placeholder="Selecione um vídeo"
            options={
              videosData?.data?.map((video) => ({
                label: video.title,
                value: video.secureId,
              })) || []
            }
            value={watch("videoId")}
            onValueChange={(value) => setValue("videoId", value as string)}
            error={errors.videoId}
            isInvalid={!!errors.videoId}
          />

          <Field.Root invalid={!!errors.order}>
            <Field.Label color="white">Ordem</Field.Label>
            <Input
              type="number"
              placeholder="Digite a ordem da questão"
              bg="gray.700"
              border="1px solid"
              borderColor="gray.600"
              color="white"
              _placeholder={{ color: "gray.400" }}
              _focus={{
                borderColor: "blue.400",
                boxShadow: "0 0 0 1px #3182ce",
              }}
              {...register("order")}
            />
            <Field.ErrorText>{errors.order?.message}</Field.ErrorText>
          </Field.Root>
        </HStack>
        <Tabs.Root
          defaultValue="essay"
          variant={"subtle"}
          mt={4}
          onValueChange={(details) => {
            setValue("questionType", details.value as "essay" | "objective");
            setCurrentQuestionType(details.value as "essay" | "objective");
          }}
        >
          <Tabs.List gap={4}>
            <Tabs.Trigger
              value="essay"
              outline={"1px solid rgb(134, 134, 141, 0.5)"}
            >
              Dissertativa
            </Tabs.Trigger>
            <Tabs.Trigger
              value="objective"
              outline={"1px solid rgb(134, 134, 141, 0.5)"}
            >
              Objetiva
            </Tabs.Trigger>
          </Tabs.List>
          <Tabs.Content value="essay">
            <VStack gap={4} align="stretch">
              <Field.Root invalid={!!errors.essayStatement}>
                <Field.Label color="white">Enunciado</Field.Label>
                <Textarea
                  placeholder="Digite o enunciado da questão dissertativa"
                  bg="gray.700"
                  border="1px solid"
                  borderColor="gray.600"
                  color="white"
                  _placeholder={{ color: "gray.400" }}
                  _focus={{
                    borderColor: "blue.400",
                    boxShadow: "0 0 0 1px #3182ce",
                  }}
                  {...register("essayStatement")}
                />
                <Field.ErrorText>
                  {errors.essayStatement?.message}
                </Field.ErrorText>
              </Field.Root>
            </VStack>
          </Tabs.Content>
          <Tabs.Content value="objective">
            <VStack gap={4} align="stretch">
              <Field.Root invalid={!!errors.objectiveStatement}>
                <Field.Label color="white">Enunciado</Field.Label>
                <Textarea
                  placeholder="Digite o enunciado da questão objetiva"
                  bg="gray.700"
                  border="1px solid"
                  borderColor="gray.600"
                  color="white"
                  _placeholder={{ color: "gray.400" }}
                  _focus={{
                    borderColor: "blue.400",
                    boxShadow: "0 0 0 1px #3182ce",
                  }}
                  {...register("objectiveStatement")}
                />
                <Field.ErrorText>
                  {errors.objectiveStatement?.message}
                </Field.ErrorText>
              </Field.Root>

              <HStack gap={4} align="end">
                <FormCombobox
                  label="Variável"
                  placeholder="Selecione uma variável"
                  options={variablesData.map((variable) => ({
                    label: variable.name,
                    value: variable.secureId,
                  }))}
                  value={watch("selectedVariable") || ""}
                  onValueChange={(value) =>
                    setValue("selectedVariable", value as string)
                  }
                />

                <Field.Root invalid={!!errors.variablePercent}>
                  <Field.Label color="white">Porcentagem</Field.Label>
                  <InputGroup endElement={<Text>%</Text>}>
                    <Input
                      type="number"
                      placeholder="0"
                      bg="gray.700"
                      border="1px solid"
                      borderColor="gray.600"
                      color="white"
                      _placeholder={{ color: "gray.400" }}
                      _focus={{
                        borderColor: "blue.400",
                        boxShadow: "0 0 0 1px #3182ce",
                      }}
                      {...register("variablePercent", { valueAsNumber: true })}
                    />
                  </InputGroup>
                  <Field.ErrorText>
                    {errors.variablePercent?.message}
                  </Field.ErrorText>
                </Field.Root>

                <DefaultButton
                  tooltipContent="Adicionar Variável"
                  buttonColor="#156082"
                  size="md"
                  onClick={handleAddVariable}
                  type="button"
                >
                  <LuPlus />
                </DefaultButton>
              </HStack>

              {/* Variables List */}
              {watch("variables") && watch("variables")!.length > 0 && (
                <Box>
                  <Text color="white" fontSize="sm" mb={2}>
                    Variáveis Adicionadas:
                  </Text>
                  <VStack gap={2} align="stretch">
                    {watch("variables")!.map((variable, index) => (
                      <HStack
                        key={index}
                        p={3}
                        bg="gray.700"
                        borderRadius="md"
                        justify="space-between"
                      >
                        <Text color="white">
                          {variablesData.find(
                            (v) => v.secureId === variable.variable
                          )?.name || variable.variable}
                        </Text>
                        <HStack>
                          <Text color="white">{variable.percent}%</Text>
                          <DefaultButton
                            size="sm"
                            buttonColor="red.500"
                            onClick={() => handleRemoveVariable(index)}
                            type="button"
                          >
                            <LuTrash2 />
                          </DefaultButton>
                        </HStack>
                      </HStack>
                    ))}
                    <Text color="gray.400" fontSize="sm">
                      Total:{" "}
                      {watch("variables")!.reduce(
                        (sum, v) => sum + v.percent,
                        0
                      )}
                      %
                    </Text>
                  </VStack>
                </Box>
              )}

              {errors.variables && (
                <Text color="red.400" fontSize="sm">
                  {errors.variables.message}
                </Text>
              )}
            </VStack>
          </Tabs.Content>
        </Tabs.Root>
      </BasicModal>

      {/* Edit Question Modal */}
      <BasicModal
        open={isEditModalOpen}
        setOpen={setIsEditModalOpen}
        title="Editar Questão"
        size="lg"
        asForm={true}
        handleSubmit={handleSubmit(handleEditQuestion)}
        isSubmitting={formState.isSubmitting}
        confirmText="Salvar"
        cancelText="Cancelar"
        placement="center"
      >
        <HStack gap={4} align="stretch">
          <FormCombobox
            label="Vídeo de Introdução"
            placeholder="Selecione um vídeo"
            options={
              videosData?.data?.map((video) => ({
                label: video.title,
                value: video.secureId,
              })) || []
            }
            value={watch("videoId")}
            onValueChange={(value) => setValue("videoId", value as string)}
            error={errors.videoId}
            isInvalid={!!errors.videoId}
          />

          <Field.Root invalid={!!errors.order}>
            <Field.Label color="white">Ordem</Field.Label>
            <Input
              type="number"
              placeholder="Digite a ordem da questão"
              bg="gray.700"
              border="1px solid"
              borderColor="gray.600"
              color="white"
              _placeholder={{ color: "gray.400" }}
              _focus={{
                borderColor: "blue.400",
                boxShadow: "0 0 0 1px #3182ce",
              }}
              {...register("order")}
            />
            <Field.ErrorText>{errors.order?.message}</Field.ErrorText>
          </Field.Root>
        </HStack>
        <Tabs.Root
          value={currentQuestionType}
          variant={"subtle"}
          mt={4}
          onValueChange={(details) => {
            setValue("questionType", details.value as "essay" | "objective");
            setCurrentQuestionType(details.value as "essay" | "objective");
          }}
        >
          <Tabs.List gap={4}>
            <Tabs.Trigger
              value="essay"
              outline={"1px solid rgb(134, 134, 141, 0.5)"}
            >
              Dissertativa
            </Tabs.Trigger>
            <Tabs.Trigger
              value="objective"
              outline={"1px solid rgb(134, 134, 141, 0.5)"}
            >
              Objetiva
            </Tabs.Trigger>
          </Tabs.List>
          <Tabs.Content value="essay">
            <VStack gap={4} align="stretch">
              <Field.Root invalid={!!errors.essayStatement}>
                <Field.Label color="white">Enunciado</Field.Label>
                <Textarea
                  placeholder="Digite o enunciado da questão dissertativa"
                  bg="gray.700"
                  border="1px solid"
                  borderColor="gray.600"
                  color="white"
                  _placeholder={{ color: "gray.400" }}
                  _focus={{
                    borderColor: "blue.400",
                    boxShadow: "0 0 0 1px #3182ce",
                  }}
                  {...register("essayStatement")}
                />
                <Field.ErrorText>
                  {errors.essayStatement?.message}
                </Field.ErrorText>
              </Field.Root>
            </VStack>
          </Tabs.Content>
          <Tabs.Content value="objective">
            <VStack gap={4} align="stretch">
              <Field.Root invalid={!!errors.objectiveStatement}>
                <Field.Label color="white">Enunciado</Field.Label>
                <Textarea
                  placeholder="Digite o enunciado da questão objetiva"
                  bg="gray.700"
                  border="1px solid"
                  borderColor="gray.600"
                  color="white"
                  _placeholder={{ color: "gray.400" }}
                  _focus={{
                    borderColor: "blue.400",
                    boxShadow: "0 0 0 1px #3182ce",
                  }}
                  {...register("objectiveStatement")}
                />
                <Field.ErrorText>
                  {errors.objectiveStatement?.message}
                </Field.ErrorText>
              </Field.Root>

              {/* Variables List */}
              {watch("variables") && watch("variables")!.length > 0 && (
                <Box>
                  <Text color="white" fontSize="sm" mb={2}>
                    Variáveis Adicionadas:
                  </Text>
                  <VStack gap={2} align="stretch">
                    {watch("variables")!.map((variable, index) => (
                      <HStack
                        key={index}
                        p={3}
                        bg="gray.700"
                        borderRadius="md"
                        justify="space-between"
                      >
                        <Text color="white">
                          {variablesData.find(
                            (v) => v.secureId === variable.variable
                          )?.name || variable.variable}
                        </Text>
                        <HStack>
                          <Text color="white">{variable.percent}%</Text>
                          <DefaultButton
                            size="sm"
                            buttonColor="red.500"
                            onClick={() => handleRemoveVariable(index)}
                            type="button"
                          >
                            <LuTrash2 />
                          </DefaultButton>
                        </HStack>
                      </HStack>
                    ))}
                    <Text color="gray.400" fontSize="sm">
                      Total:{" "}
                      {watch("variables")!.reduce(
                        (sum, v) => sum + v.percent,
                        0
                      )}
                      %
                    </Text>
                  </VStack>
                </Box>
              )}

              <HStack gap={4} align="end">
                <FormCombobox
                  label="Variável"
                  placeholder="Selecione uma variável"
                  options={variablesData.map((variable) => ({
                    label: variable.name,
                    value: variable.secureId,
                  }))}
                  value={watch("selectedVariable") || ""}
                  onValueChange={(value) =>
                    setValue("selectedVariable", value as string)
                  }
                />

                <Field.Root invalid={!!errors.variablePercent}>
                  <Field.Label color="white">Porcentagem</Field.Label>
                  <InputGroup endElement={<Text>%</Text>}>
                    <Input
                      type="number"
                      placeholder="0"
                      bg="gray.700"
                      border="1px solid"
                      borderColor="gray.600"
                      color="white"
                      _placeholder={{ color: "gray.400" }}
                      _focus={{
                        borderColor: "blue.400",
                        boxShadow: "0 0 0 1px #3182ce",
                      }}
                      {...register("variablePercent", { valueAsNumber: true })}
                    />
                  </InputGroup>
                  <Field.ErrorText>
                    {errors.variablePercent?.message}
                  </Field.ErrorText>
                </Field.Root>

                <DefaultButton
                  tooltipContent="Adicionar Variável"
                  buttonColor="#156082"
                  size="md"
                  onClick={handleAddVariable}
                  type="button"
                >
                  <LuPlus />
                </DefaultButton>
              </HStack>

              {errors.variables && (
                <Text color="red.400" fontSize="sm">
                  {errors.variables.message}
                </Text>
              )}
            </VStack>
          </Tabs.Content>
        </Tabs.Root>
      </BasicModal>

      {/* Delete Question Modal */}
      <BasicModal
        open={isDeleteModalOpen}
        setOpen={setIsDeleteModalOpen}
        title="Excluir Questão"
        size="sm"
        handleConfirm={handleDeleteQuestion}
        confirmText="Excluir"
        cancelText="Cancelar"
        placement="center"
        confirmButtonColor="red.500"
      >
        <VStack gap={4} align="center">
          <Text fontSize="md" color="white" textAlign="center">
            Você tem certeza que deseja excluir essa questão?
          </Text>
          <Text fontSize="sm" color="gray.400" textAlign="center">
            Esta ação não pode ser desfeita.
          </Text>
        </VStack>
      </BasicModal>
    </Flex>
  );
}
