"use client";
import Default<PERSON><PERSON>on from "@/components/global/buttons/button";
import {
  Box,
  Field,
  Flex,
  HStack,
  Icon,
  Input,
  InputGroup,
  Stack,
  Table,
  Tabs,
  Text,
  Textarea,
  VStack,
} from "@chakra-ui/react";
import { useState } from "react";
import { LuSearch, LuPlus, LuTrash2, LuPencil } from "react-icons/lu";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { formatInTimeZone } from "date-fns-tz";
import BasicModal from "@/components/global/modal/basic-modal";
import { useMutation } from "@tanstack/react-query";
import FormCombobox from "@/components/global/combobox/form-combobox";
import { useGetAllQuestions } from "@/hook/questions/useGetAllQuestions";
import { GetQuestionDto } from "@/utils/types/DTO/questions/questions.dto";
import { useGetAllVideos } from "@/hook/videos/useGetAllVideos";

type VariableItem = {
  variable: string;
  percent: number;
};

type NewQuestionFormData = {
  essayQuestion: {
    statement: string;
  };
  objectiveQuestion: {
    statement: string;
    variables: VariableItem[];
  };
  videoId: string;
  order: number;
};

const VariableItemSchema = yup.object().shape({
  variable: yup.string().required("A variável é obrigatória."),
  percent: yup
    .number()
    .typeError("A porcentagem deve ser um número.")
    .min(0, "A porcentagem não pode ser negativa.")
    .max(100, "A porcentagem não pode ser maior que 100.")
    .required("A porcentagem é obrigatória."),
});

const NewQuestionSchema: yup.ObjectSchema<NewQuestionFormData> = yup
  .object()
  .shape({
    essayQuestion: yup.object().shape({
      statement: yup
        .string()
        .required("O enunciado da questão dissertativa é obrigatório."),
    }),
    objectiveQuestion: yup.object().shape({
      statement: yup
        .string()
        .required("O enunciado da questão objetiva é obrigatório."),
      variables: yup
        .array()
        .of(VariableItemSchema)
        .min(1, "Adicione pelo menos uma variável.")
        .required(),
    }),
    videoId: yup
      .string()
      .uuid("ID do vídeo inválido.")
      .required("O ID do vídeo é obrigatório."),
    order: yup
      .number()
      .typeError("A ordem deve ser um número.")
      .positive("A ordem deve ser um número positivo.")
      .integer("A ordem deve ser um número inteiro.")
      .required("A ordem é obrigatória."),
  });

export default function Questions() {
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedQuestion, setSelectedQuestion] =
    useState<GetQuestionDto | null>(null);
  const [searchTerm, setSearchTerm] = useState("");

  const { data: questionsData } = useGetAllQuestions(searchTerm);
  const { data: videosData } = useGetAllVideos();

  const {
    register,
    handleSubmit,
    reset,
    setValue,
    watch,
    formState,
    formState: { errors, isSubmitting },
  } = useForm<NewQuestionFormData>({
    resolver: yupResolver(NewQuestionSchema),
    defaultValues: {},
  });

  const addQuestion = useMutation({});

  const editQuestion = useMutation({});

  const deleteQuestion = useMutation({});

  const handleAddQuestion = async (data: NewQuestionFormData) => {
    try {
    } catch (e) {}
  };

  const handleEditQuestion = async (data: NewQuestionFormData) => {
    try {
    } catch (e) {}
  };

  const handleDeleteQuestion = async () => {
    try {
    } catch (e) {}
  };

  const handleOpenAddModal = () => {
    reset();
    setIsAddModalOpen(true);
  };

  const handleOpenEditModal = (question: GetQuestionDto) => {
    setSelectedQuestion(question);
    setIsEditModalOpen(true);
  };

  const handleOpenDeleteModal = (question: GetQuestionDto) => {
    setSelectedQuestion(question);
    setIsDeleteModalOpen(true);
  };

  return (
    <Flex flex={1} position={"relative"} overflow={"hidden"} p={4}>
      <Stack w="100%" gap={6}>
        {/* Header */}
        <Text fontSize="2xl" fontWeight="bold" color="white">
          Questões
        </Text>

        {/* Search and Filters */}
        <HStack gap={4} justify="space-between">
          <Box position="relative" flex={1} maxW="400px">
            <Input
              placeholder="Buscar..."
              bg="gray.800"
              border="1px solid"
              borderColor="gray.600"
              color="white"
              _placeholder={{ color: "gray.400" }}
              _focus={{
                borderColor: "blue.400",
                boxShadow: "0 0 0 1px #3182ce",
              }}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
            <Box
              position="absolute"
              right={3}
              top="50%"
              transform="translateY(-50%)"
              color="gray.400"
            >
              <LuSearch />
            </Box>
          </Box>
          <DefaultButton onClick={handleOpenAddModal} size="md">
            <LuPlus />
            Adicionar Questões
          </DefaultButton>
        </HStack>

        {/* Table */}
        <Box
          bg="gray.800"
          borderRadius="lg"
          border="1px solid"
          borderColor="gray.600"
          overflow="hidden"
        >
          <Table.Root size="md" variant="outline">
            <Table.Header bg="gray.700">
              <Table.Row>
                <Table.ColumnHeader color="white" fontWeight="bold">
                  Enunciado Dissertativa
                </Table.ColumnHeader>
                <Table.ColumnHeader color="white" fontWeight="bold">
                  Enunciado Objetiva
                </Table.ColumnHeader>
                <Table.ColumnHeader color="white" fontWeight="bold">
                  Ordem
                </Table.ColumnHeader>
                <Table.ColumnHeader color="white" fontWeight="bold">
                  Data de Criação
                </Table.ColumnHeader>
                <Table.ColumnHeader color="white" fontWeight="bold">
                  <Flex justify="center">Ações</Flex>
                </Table.ColumnHeader>
              </Table.Row>
            </Table.Header>
            <Table.Body>
              {questionsData?.data?.map((item) => (
                <Table.Row key={item.secureId} _hover={{ bg: "gray.700" }}>
                  <Table.Cell>
                    <Text color="gray.300">{item.essayQuestion.statement}</Text>
                  </Table.Cell>
                  <Table.Cell>
                    <Text color="gray.300">
                      {item.objectiveQuestion.statement}
                    </Text>
                  </Table.Cell>
                  <Table.Cell>
                    <Text color="gray.300">{item.order}</Text>
                  </Table.Cell>
                  <Table.Cell>
                    <Text color="gray.300" maxW="200px">
                      {formatInTimeZone(
                        item.createdAt,
                        "America/Sao_Paulo",
                        "dd/MM/yyyy HH:mm:ss"
                      )}
                    </Text>
                  </Table.Cell>
                  <Table.Cell>
                    <HStack gap={2} justifyContent={"center"}>
                      <DefaultButton
                        tooltipContent="Editar"
                        buttonColor="#156082"
                        size="sm"
                        onClick={() => handleOpenEditModal(item)}
                      >
                        <LuPencil />
                      </DefaultButton>
                      <DefaultButton
                        tooltipContent="Excluir"
                        buttonColor="red.500"
                        size="sm"
                        onClick={() => handleOpenDeleteModal(item)}
                      >
                        <LuTrash2 />
                      </DefaultButton>
                    </HStack>
                  </Table.Cell>
                </Table.Row>
              )) || (
                <Table.Row>
                  <Table.Cell colSpan={4}>
                    <Text color="gray.400" textAlign="center" py={4}>
                      Nenhuma questão encontrada
                    </Text>
                  </Table.Cell>
                </Table.Row>
              )}
            </Table.Body>
          </Table.Root>
        </Box>
      </Stack>

      {/* Add Question Modal */}
      <BasicModal
        open={isAddModalOpen}
        setOpen={setIsAddModalOpen}
        title="Adicionar Grupo de Questões"
        size="lg"
        asForm={true}
        handleSubmit={handleSubmit(handleAddQuestion)}
        isSubmitting={formState.isSubmitting}
        confirmText="Adicionar"
        cancelText="Cancelar"
        placement="center"
      >
        <HStack gap={4} align="stretch">
          <FormCombobox
            label="Vídeo de Introdução"
            placeholder="Selecione um vídeo"
            options={
              videosData?.data?.map((video) => ({
                label: video.title,
                value: video.secureId,
              })) || []
            }
            value={watch("videoId")}
            onValueChange={(value) => setValue("videoId", value as string)}
            error={errors.videoId}
            isInvalid={!!errors.videoId}
          />

          <Field.Root invalid={!!errors.order}>
            <Field.Label color="white">Ordem</Field.Label>
            <Input
              type="number"
              placeholder="Digite a ordem da questão"
              bg="gray.700"
              border="1px solid"
              borderColor="gray.600"
              color="white"
              _placeholder={{ color: "gray.400" }}
              _focus={{
                borderColor: "blue.400",
                boxShadow: "0 0 0 1px #3182ce",
              }}
              {...register("order")}
            />
            <Field.ErrorText>{errors.order?.message}</Field.ErrorText>
          </Field.Root>
        </HStack>
        <Tabs.Root defaultValue="essay" variant={"subtle"} mt={4}>
          <Tabs.List gap={4}>
            <Tabs.Trigger
              value="essay"
              outline={"1px solid rgb(134, 134, 141, 0.5)"}
            >
              Dissertativa
            </Tabs.Trigger>
            <Tabs.Trigger
              value="objective"
              outline={"1px solid rgb(134, 134, 141, 0.5)"}
            >
              Objetiva
            </Tabs.Trigger>
          </Tabs.List>
          <Tabs.Content value="essay">
            <VStack gap={4} align="stretch">
              <Field.Root invalid={!!errors.essayQuestion}>
                <Field.Label color="white">Enunciado</Field.Label>
                <Textarea
                  placeholder="Digite o enunciado da questão dissertativa"
                  bg="gray.700"
                  border="1px solid"
                  borderColor="gray.600"
                  color="white"
                  _placeholder={{ color: "gray.400" }}
                  _focus={{
                    borderColor: "blue.400",
                    boxShadow: "0 0 0 1px #3182ce",
                  }}
                  {...register("essayQuestion")}
                />
                <Field.ErrorText>
                  {errors.essayQuestion?.message}
                </Field.ErrorText>
              </Field.Root>
            </VStack>
          </Tabs.Content>
          <Tabs.Content value="objective">
            <VStack gap={4} align="stretch">
              <Field.Root invalid={!!errors.objectiveQuestion}>
                <Field.Label color="white">Enunciado</Field.Label>
                <Textarea
                  placeholder="Digite o enunciado da questão objetiva"
                  bg="gray.700"
                  border="1px solid"
                  borderColor="gray.600"
                  color="white"
                  _placeholder={{ color: "gray.400" }}
                  _focus={{
                    borderColor: "blue.400",
                    boxShadow: "0 0 0 1px #3182ce",
                  }}
                  {...register("objectiveQuestion")}
                />
                <Field.ErrorText>
                  {errors.objectiveQuestion?.message}
                </Field.ErrorText>
              </Field.Root>

              <HStack gap={4} align="end">
                <FormCombobox
                  label="Variável"
                  placeholder="Selecione uma variável"
                  options={[]}
                  value={watch("variables")}
                  onValueChange={(value) => setValue("variables", value as any)}
                  error={errors.variables}
                  isInvalid={!!errors.variables}
                />

                <Field.Root invalid={!!errors.percent}>
                  <Field.Label color="white">Porcentagem</Field.Label>
                  <InputGroup endElement={<Text>%</Text>}>
                    <Input
                      placeholder=""
                      bg="gray.700"
                      border="1px solid"
                      borderColor="gray.600"
                      color="white"
                      _placeholder={{ color: "gray.400" }}
                      _focus={{
                        borderColor: "blue.400",
                        boxShadow: "0 0 0 1px #3182ce",
                      }}
                      {...register("order")}
                    />
                  </InputGroup>
                  <Field.ErrorText>{errors.order?.message}</Field.ErrorText>
                </Field.Root>

                <DefaultButton
                  tooltipContent="Adicionar"
                  buttonColor="#156082"
                  size="md"
                >
                  <LuPlus />
                </DefaultButton>
              </HStack>
            </VStack>
          </Tabs.Content>
        </Tabs.Root>
      </BasicModal>

      {/* Edit Question Modal */}
      <BasicModal
        open={isEditModalOpen}
        setOpen={setIsEditModalOpen}
        title="Editar Vídeo"
        size="md"
        asForm={true}
        handleSubmit={handleSubmit(handleEditQuestion)}
        isSubmitting={formState.isSubmitting}
        confirmText="Salvar"
        cancelText="Cancelar"
        placement="center"
      >
        <VStack gap={4} align="stretch"></VStack>
      </BasicModal>

      {/* Delete Question Modal */}
      <BasicModal
        open={isDeleteModalOpen}
        setOpen={setIsDeleteModalOpen}
        title="Excluir Vídeo"
        size="sm"
        handleConfirm={handleDeleteQuestion}
        confirmText="Excluir"
        cancelText="Cancelar"
        placement="center"
        confirmButtonColor="red.500"
      >
        <VStack gap={4} align="center">
          <Text fontSize="md" color="white" textAlign="center">
            Você tem certeza que deseja excluir essa questão?
          </Text>
          <Text fontSize="sm" color="gray.400" textAlign="center">
            Esta ação não pode ser desfeita.
          </Text>
        </VStack>
      </BasicModal>
    </Flex>
  );
}
