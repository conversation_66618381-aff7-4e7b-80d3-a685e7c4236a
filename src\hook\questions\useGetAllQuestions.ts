import { api } from "@/services/api";
import { GetAllQuestionsDto } from "@/utils/types/DTO/questions/questions.dto";
import { useQuery } from "@tanstack/react-query";

async function getAllQuestions(searchTerm?: string) {
  const { data } = await api.get<GetAllQuestionsDto>(`/management/question`, {
    params: {
      search: searchTerm,
    },
  });
  return data;
}

export function useGetAllQuestions(searchTerm?: string) {
  return useQuery({
    queryKey: ["questions", searchTerm],
    queryFn: async () => await getAllQuestions(searchTerm),
  });
}